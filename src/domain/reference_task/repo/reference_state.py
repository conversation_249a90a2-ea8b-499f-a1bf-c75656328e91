#!/usr/bin/env python3
"""
参考任务状态定义

基于现有DeploymentState的扩展，保持字段兼容性
"""

from typing import TypedDict, List, Dict, Any, Optional, Callable, Annotated
from langgraph.graph.message import add_messages


class ReferenceTaskState(TypedDict):
    """参考任务执行状态 - 兼容DeploymentState字段"""

    # Task related (与DeploymentState保持一致)
    task: str  # User input task description
    task_id: str  # Task ID for stop control
    task_steps: List[str]  # Parsed individual task steps
    completed: bool  # Whether the task is completed
    execution_status: str  # Execution status (processing/succeed/failed/terminate)
    retry_count: int  # Current retry count
    max_retries: int  # Maximum retry count
    step_failed: bool  # Whether the current step failed
    error_message: Optional[str]  # Error message if execution failed
    test_case_name: str
    test_case_description: str
    expected_result: str
    app_package: str  # 应用包名
    execution_count: int  # 当前执行次数
    is_restart: bool  # 是否重启app

    # Device related (与DeploymentState保持一致)
    device: str  # Device identifier
    device_type: str  # Device type (android/ios)
    device_config: Dict[str, Any]  # Device configuration

    # Agent related (与DeploymentState保持一致)
    agent_config_id: str  # Agent configuration ID

    # Verification related (与DeploymentState保持一致)
    verification_mode: str  # Verification mode
    step_expected_results: Optional[List[Dict[str, Any]]]  # Expected results for each step
    current_step_index: int  # Current step index
    verification_failure_reason: Optional[str]  # Verification failure reason
    execution_blocked: bool  # Whether execution is blocked
    block_reason: Optional[str]  # Block reason

    # Prompt customization (与DeploymentState保持一致)
    app_name: Optional[str]  # 应用名称
    app_description: Optional[str]  # 应用描述
    ui_component_instructions: Optional[str]  # UI组件操作说明
    special_scenarios: Optional[str]  # 特殊场景处理说明

    # Supervisor related (与DeploymentState保持一致)
    supervisor_state: Optional[Dict[str, Any]]  # 监督状态

    # Page information (与DeploymentState保持一致)
    current_page: Dict  # Current page information, including screenshot path and element data

    # Records and messages (与DeploymentState保持一致)
    history: List[Dict]  # Execution history records
    messages: Annotated[list, add_messages]  # Message history for React mode
    decision_fields: Optional[Dict[str, Any]]  # Latest decision agent parsed fields

    # Callback (与DeploymentState保持一致)
    callback: Optional[Callable[[TypedDict], None]]  # Callback function

    # 参考任务特有字段
    reference_task_id: str  # 参考任务ID
    reference_actions: List[Dict[str, Any]]  # 参考任务动作列表
