#!/usr/bin/env python3
"""
参考任务应用服务

基于参考任务的动作列表，让模型参考执行新的用例
"""

from typing import Dict, Any

from loguru import logger

from src.domain.reference_task.service.reference_task_service import reference_task_service
from src.domain.ui_task.mobile.repo import task_stop_manager
from src.domain.ui_task.mobile.service.keyboard_service import keyboard_service
from src.domain.ui_task.mobile.service.task_persistence_service import task_persistence_service


class ReferenceTaskApplication:
    """参考任务应用服务"""

    @staticmethod
    def execute_reference_task(
            task_id: str,
            request: Any
    ) -> Dict[str, Any]:
        """
        执行参考任务
        
        Args:
            task_id: 任务ID
            request: 参考任务执行请求
            
        Returns:
            执行结果
        """
        try:
            logger.info(f"[{task_id}] 🚀 Starting reference task execution...")
            logger.info(f"[{task_id}] 📋 Task: {request.task_name}")
            logger.info(f"[{task_id}] 🔗 Reference task ID: {request.reference_task_id}")
            
            # 调用领域服务执行参考任务
            result = reference_task_service.execute_with_reference(
                task_id=task_id,
                request=request
            )
            
            logger.info(f"[{task_id}] ✅ Reference task execution completed")
            return result
            
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Reference task execution failed: {str(e)}")
            return {
                "success": False,
                "message": f"Reference task execution failed: {str(e)}",
                "task_id": task_id
            }

    @staticmethod
    def create_reference_task_from_request(task_id: str, request: Any, device_id: str):
        """
        从请求创建参考任务记录

        Args:
            task_id: 任务ID
            request: 参考任务执行请求
            device_id: 设备ID

        Returns:
            创建的任务对象
        """
        try:
            # 创建任务记录（复用现有的任务表结构）
            task = task_persistence_service.create_task_from_request(task_id, request, device_id)

            logger.info(f"[{task_id}] ✅ Reference task record created")
            return task

        except Exception as e:
            logger.error(f"[{task_id}] ❌ Failed to create reference task record: {str(e)}")
            return None

    @staticmethod
    def get_task_by_id(task_id: str):
        """获取任务信息"""
        return task_persistence_service.get_task_by_task_id(task_id)

    @staticmethod
    def stop_task(task_id: str) -> bool:
        """停止任务"""
        # 停止执行线程
        thread_stopped = task_stop_manager.stop_task(task_id)
        # 更新数据库状态
        db_updated = task_persistence_service.stop_task_execution(task_id)
        return thread_stopped and db_updated

    @staticmethod
    def register_task(task_id: str, task_name: str):
        """注册任务到停止管理器"""
        task_stop_manager.register_task(task_id, task_name)

    @staticmethod
    def unregister_task(task_id: str):
        """注销任务"""
        task_stop_manager.unregister_task(task_id)

    @staticmethod
    def start_task_execution(task_id: str):
        """开始任务执行（更新数据库状态）"""
        task_persistence_service.start_task_execution(task_id)

    @staticmethod
    def complete_task_execution(task_id: str, success: bool, error_message: str = None):
        """完成任务执行"""
        task_persistence_service.complete_task_execution(task_id, success, error_message)

    @staticmethod
    def setup_adb_keyboards(task_id: str, device_id: str) -> bool:
        """设置ADB键盘"""
        return keyboard_service.setup_adb_keyboards_for_task(task_id, device_id)

    @staticmethod
    def is_task_stopped(task_id: str) -> bool:
        """检查任务是否被停止"""
        return task_stop_manager.is_task_stopped(task_id)

    @staticmethod
    def cleanup_adb_keyboards(task_id: str):
        """清理ADB键盘设置"""
        try:
            keyboard_service.cleanup_adb_keyboards_for_task(task_id)
            logger.info(f"[{task_id}] ✅ ADB keyboards cleaned up")
        except Exception as e:
            logger.error(f"[{task_id}] ❌ Error cleaning up ADB keyboards: {str(e)}")


# 创建全局实例
reference_task_application = ReferenceTaskApplication()
